#!/usr/bin/env python3
"""
测试重构后的代码结构
"""

import sys
import os

# 添加backend到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_imports():
    """测试所有重构后的导入"""
    print("🔍 测试重构后的模块导入...")
    
    try:
        # 测试配置
        from config import Config
        print("✅ Config 导入成功")
        
        # 测试嵌入客户端
        from clients.embedding_client import EmbeddingClient, get_embedding_client
        print("✅ EmbeddingClient 导入成功")
        
        # 测试向量数据库模型
        from models.vector_database import VectorDatabase, UserMemoryVectorDB, PersonaMemoryVectorDB
        print("✅ 向量数据库模型导入成功")
        
        # 测试向量记忆管理器
        from services.vector_memory_manager import VectorMemoryManager
        print("✅ VectorMemoryManager 导入成功")
        
        # 测试个人记忆管理器
        from services.persona_memory_manager import VectorPersonaMemoryManager
        print("✅ VectorPersonaMemoryManager 导入成功")
        
        print("\n🎉 所有模块导入测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_structure():
    """测试代码结构"""
    print("\n📁 检查重构后的文件结构...")
    
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    
    # 检查新的文件结构
    files_to_check = [
        'clients/embedding_client.py',
        'models/vector_database.py',
        'services/vector_memory_manager.py',
        'services/persona_memory_manager.py'
    ]
    
    missing_files = []
    for file_path in files_to_check:
        full_path = os.path.join(backend_dir, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            missing_files.append(file_path)
    
    # 检查旧文件是否已删除
    old_file = os.path.join(backend_dir, 'services/embedding_service.py')
    if not os.path.exists(old_file):
        print("✅ 旧的 embedding_service.py 已删除")
    else:
        print("⚠️ 旧的 embedding_service.py 仍然存在")
    
    if not missing_files:
        print("\n🎉 文件结构检查通过！")
        return True
    else:
        print(f"\n❌ 缺少文件: {missing_files}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试重构结果...\n")
    
    # 测试文件结构
    structure_ok = test_structure()
    
    # 测试导入
    import_ok = test_imports()
    
    print("\n" + "="*50)
    if structure_ok and import_ok:
        print("🎉 重构测试全部通过！")
        print("\n📋 重构总结:")
        print("1. ✅ embedding_service 已简化并移动到 clients 包")
        print("2. ✅ 向量数据库操作已移动到 models 包")
        print("3. ✅ 所有服务已更新为使用新的模块结构")
        print("4. ✅ 嵌入维度不匹配问题已修复")
        return True
    else:
        print("❌ 重构测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

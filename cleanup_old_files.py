#!/usr/bin/env python3
"""
清理旧的向量数据库相关文件
在迁移到Memobase后，这些文件不再需要
"""

import os
import shutil

def cleanup_old_files():
    """清理旧文件"""
    print("🧹 开始清理旧的向量数据库相关文件...")
    
    # 要删除的文件列表
    files_to_remove = [
        "backend/models/vector_database.py",
        "backend/services/vector_memory_manager.py", 
        "backend/services/persona_memory_manager.py",  # 保留SQLite版本，删除向量版本
        "backend/clients/embedding_client.py",
        "backend/test_imports.py",
        "backend/test_memory_search.py",
        "backend/debug_search.py",
        "test_refactor.py"
    ]
    
    # 要删除的目录列表
    dirs_to_remove = [
        "chroma_db",  # ChromaDB数据目录
        "backend/chroma_db"
    ]
    
    removed_files = []
    removed_dirs = []
    
    # 删除文件
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                removed_files.append(file_path)
                print(f"✅ 删除文件: {file_path}")
            except Exception as e:
                print(f"❌ 删除文件失败 {file_path}: {e}")
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    # 删除目录
    for dir_path in dirs_to_remove:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                removed_dirs.append(dir_path)
                print(f"✅ 删除目录: {dir_path}")
            except Exception as e:
                print(f"❌ 删除目录失败 {dir_path}: {e}")
        else:
            print(f"⚠️ 目录不存在: {dir_path}")
    
    print(f"\n📊 清理完成:")
    print(f"   删除文件: {len(removed_files)} 个")
    print(f"   删除目录: {len(removed_dirs)} 个")
    
    if removed_files or removed_dirs:
        print("\n⚠️ 注意: 如果需要回滚到向量数据库版本，请从Git历史中恢复这些文件")
    
    return removed_files, removed_dirs

def update_persona_memory_manager():
    """更新persona_memory_manager.py，只保留SQLite版本"""
    print("\n🔧 更新persona_memory_manager.py...")
    
    file_path = "backend/services/persona_memory_manager.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到VectorPersonaMemoryManager类的开始位置
        vector_class_start = content.find("class VectorPersonaMemoryManager:")
        
        if vector_class_start != -1:
            # 只保留SQLite版本，删除向量版本
            content = content[:vector_class_start].rstrip()
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 更新完成: {file_path}")
            print("   已删除VectorPersonaMemoryManager类，保留SQLite版本")
        else:
            print("⚠️ 未找到VectorPersonaMemoryManager类")
    
    except Exception as e:
        print(f"❌ 更新文件失败: {e}")

def main():
    """主函数"""
    print("🚀 Memobase迁移 - 清理旧文件")
    print("=" * 50)
    
    # 确认操作
    response = input("确定要删除旧的向量数据库相关文件吗？(y/N): ")
    if response.lower() not in ['y', 'yes', '是']:
        print("❌ 操作已取消")
        return
    
    # 清理文件
    removed_files, removed_dirs = cleanup_old_files()
    
    # 更新persona_memory_manager.py
    update_persona_memory_manager()
    
    print("\n🎉 清理完成！")
    print("\n📝 后续步骤:")
    print("1. 确保Memobase服务正在运行")
    print("2. 更新环境变量配置")
    print("3. 安装新依赖: pip install -r backend/requirements.txt")
    print("4. 测试系统: python start.py")

if __name__ == "__main__":
    main()

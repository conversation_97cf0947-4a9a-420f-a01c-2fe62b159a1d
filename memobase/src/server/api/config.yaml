# Memobase配置文件 - 虚拟人恋爱陪伴系统
# 基于profile_for_companion配置，针对25岁女性心理咨询师虚拟人优化

# LLM配置 - 使用火山引擎豆包模型
llm_api_key: "YOUR_VOLCENGINE_API_KEY"
llm_base_url: "https://ark.cn-beijing.volces.com/api/v3/"
best_llm_model: "ep-20241218155714-8wnzh"  # 豆包模型endpoint

# 嵌入模型配置 - 使用火山引擎嵌入模型
enable_event_embedding: true
embedding_api_key: "YOUR_VOLCENGINE_API_KEY"
embedding_base_url: "https://ark.cn-beijing.volces.com/api/v3/"
embedding_model: "doubao-embedding-text-240715"

# 记忆配置
max_pre_profile_token_size: 800
max_profile_subtopics: 15
max_context_token_size: 1000

# 用户画像配置 - 针对恋爱陪伴场景优化
overwrite_user_profiles:
  - topic: "basic_info"
    description: "用户基本信息"
    sub_topics:
      - name: "name"
        description: "用户姓名或昵称"
      - name: "age"
        description: "用户年龄"
      - name: "gender"
        description: "用户性别"
      - name: "occupation"
        description: "用户职业背景"
      - name: "location"
        description: "用户所在地区"
      - name: "relationship_status"
        description: "用户感情状态"
      - name: "personality"
        description: "用户性格特点"

  - topic: "interests_hobbies"
    description: "兴趣爱好"
    sub_topics:
      - name: "hobbies"
        description: "用户的兴趣爱好"
      - name: "music_preference"
        description: "音乐偏好"
      - name: "movie_preference"
        description: "电影偏好"
      - name: "food_preference"
        description: "饮食偏好"
      - name: "travel_experience"
        description: "旅行经历和偏好"
      - name: "sports_activities"
        description: "运动和活动偏好"

  - topic: "emotional_state"
    description: "情感状态和心理状况"
    sub_topics:
      - name: "current_mood"
        description: "当前情绪状态"
      - name: "stress_level"
        description: "压力水平"
      - name: "emotional_needs"
        description: "情感需求"
      - name: "support_preferences"
        description: "希望得到的支持类型"
      - name: "communication_style"
        description: "沟通风格偏好"

  - topic: "life_goals"
    description: "生活目标和价值观"
    sub_topics:
      - name: "short_term_goals"
        description: "短期目标"
      - name: "long_term_goals"
        description: "长期目标"
      - name: "values"
        description: "价值观念"
      - name: "life_philosophy"
        description: "人生哲学"
      - name: "relationship_goals"
        description: "感情目标"

  - topic: "interaction_preferences"
    description: "互动偏好"
    sub_topics:
      - name: "conversation_topics"
        description: "喜欢的聊天话题"
      - name: "interaction_frequency"
        description: "互动频率偏好"
      - name: "response_style"
        description: "希望的回复风格"
      - name: "intimacy_level"
        description: "亲密度水平"
      - name: "humor_preference"
        description: "幽默偏好"

  - topic: "memories_experiences"
    description: "重要记忆和经历"
    sub_topics:
      - name: "childhood_memories"
        description: "童年记忆"
      - name: "significant_events"
        description: "重要事件"
      - name: "relationship_history"
        description: "感情经历"
      - name: "achievements"
        description: "成就和里程碑"
      - name: "challenges_overcome"
        description: "克服的挑战"

# 事件标签配置
enable_event_tag: true
event_tags:
  - name: "mood"
    description: "情绪标签"
    values: ["happy", "sad", "excited", "anxious", "calm", "frustrated", "content"]
  - name: "topic"
    description: "话题标签"
    values: ["work", "relationship", "family", "hobbies", "health", "future_plans", "memories"]
  - name: "interaction_type"
    description: "互动类型"
    values: ["casual_chat", "emotional_support", "advice_seeking", "sharing", "planning"]

# 其他配置
enable_strict_profile: false
enable_user_context: true
context_window_size: 10
memory_retention_days: 365

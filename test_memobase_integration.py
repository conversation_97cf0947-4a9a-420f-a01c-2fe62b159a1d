#!/usr/bin/env python3
"""
测试Memobase集成
验证新的记忆系统是否正常工作
"""

import sys
import os
import traceback

# 添加backend目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_memobase_client():
    """测试Memobase客户端"""
    print("🔍 测试Memobase客户端...")
    
    try:
        from clients.memobase_client import get_memobase_client
        
        # 获取客户端
        client = get_memobase_client()
        print("✅ Memobase客户端初始化成功")
        
        # 测试连接
        if client.ping():
            print("✅ Memobase连接测试成功")
            return True
        else:
            print("❌ Memobase连接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ Memobase客户端测试失败: {e}")
        traceback.print_exc()
        return False

def test_memory_manager():
    """测试记忆管理器"""
    print("\n🧠 测试Memobase记忆管理器...")
    
    try:
        from services.memobase_memory_manager import MemobaseMemoryManager
        
        # 初始化记忆管理器
        memory_manager = MemobaseMemoryManager()
        print("✅ MemobaseMemoryManager初始化成功")
        
        # 测试记忆提取
        test_user_id = "test_user_001"
        test_text = "我叫张三，今年25岁，喜欢编程和音乐"
        
        memories = memory_manager.extract_memories_from_text(test_user_id, test_text)
        print(f"✅ 记忆提取测试成功，提取了 {len(memories)} 条记忆")
        
        # 测试获取相关记忆
        relevant_memories = memory_manager.get_relevant_memories(test_user_id, query_text="编程", limit=3)
        print(f"✅ 相关记忆获取测试成功，获取了 {len(relevant_memories)} 条记忆")
        
        # 测试用户上下文
        context = memory_manager.get_user_context(test_user_id, max_token_size=200)
        print(f"✅ 用户上下文获取测试成功，长度: {len(context)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 记忆管理器测试失败: {e}")
        traceback.print_exc()
        return False

def test_persona_memory_manager():
    """测试虚拟人记忆管理器"""
    print("\n👤 测试Memobase虚拟人记忆管理器...")
    
    try:
        from services.memobase_persona_memory_manager import MemobasePersonaMemoryManager
        
        # 初始化虚拟人记忆管理器
        persona_manager = MemobasePersonaMemoryManager()
        print("✅ MemobasePersonaMemoryManager初始化成功")
        
        # 测试获取相关记忆
        context = "你喜欢什么音乐？"
        memories = persona_manager.get_relevant_persona_memories(context, user_affection=60, limit=2)
        print(f"✅ 虚拟人记忆获取测试成功，获取了 {len(memories)} 条记忆")
        
        # 测试虚拟人上下文
        persona_context = persona_manager.get_persona_context(max_token_size=300)
        print(f"✅ 虚拟人上下文获取测试成功，长度: {len(persona_context)} 字符")
        
        # 测试统计信息
        stats = persona_manager.get_memory_stats()
        print(f"✅ 虚拟人记忆统计测试成功，总记忆数: {stats.get('total_memories', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 虚拟人记忆管理器测试失败: {e}")
        traceback.print_exc()
        return False

def test_conversation_engine():
    """测试对话引擎集成"""
    print("\n💬 测试对话引擎Memobase集成...")
    
    try:
        from services.conversation_engine import ConversationEngine
        
        # 初始化对话引擎
        engine = ConversationEngine()
        print("✅ ConversationEngine初始化成功")
        
        # 测试处理消息
        test_user_id = "test_user_002"
        test_message = "你好，我是李四，我喜欢看电影"
        
        result = engine.process_message(test_user_id, test_message)
        print(f"✅ 消息处理测试成功")
        print(f"   回复: {result['response'][:50]}...")
        print(f"   提取记忆数: {result['memories_extracted']}")
        print(f"   好感度: {result['affection_level']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 对话引擎测试失败: {e}")
        traceback.print_exc()
        return False

def test_config():
    """测试配置"""
    print("\n⚙️ 测试Memobase配置...")
    
    try:
        from config import Config
        
        # 检查Memobase配置
        memobase_config = Config.MEMOBASE_CONFIG
        print("✅ Memobase配置加载成功")
        print(f"   项目URL: {memobase_config['project_url']}")
        print(f"   API密钥: {memobase_config['api_key'][:10]}...")
        print(f"   最大Token数: {memobase_config['max_token_size']}")
        print(f"   自动刷新: {memobase_config['auto_flush']}")
        
        # 检查向量数据库配置是否已禁用
        vector_config = Config.VECTOR_DB_CONFIG
        if not vector_config['use_vector_db']:
            print("✅ 向量数据库已正确禁用")
        else:
            print("⚠️ 向量数据库仍然启用，建议禁用")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Memobase集成测试")
    print("=" * 50)
    
    tests = [
        ("配置测试", test_config),
        ("Memobase客户端", test_memobase_client),
        ("记忆管理器", test_memory_manager),
        ("虚拟人记忆管理器", test_persona_memory_manager),
        ("对话引擎集成", test_conversation_engine),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Memobase集成成功")
        print("\n📝 后续步骤:")
        print("1. 启动系统: python start.py")
        print("2. 访问前端: http://localhost:8080")
        print("3. 测试对话功能")
    else:
        print("⚠️ 部分测试失败，请检查配置和Memobase服务状态")
        print("\n🔧 故障排除:")
        print("1. 确保Memobase服务正在运行")
        print("2. 检查环境变量配置")
        print("3. 验证网络连接")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
简单的搜索测试
"""

import chromadb
from chromadb.config import Settings

def simple_search_test():
    """简单搜索测试"""
    print("🔍 简单搜索测试...")

    try:
        # 连接到现有的数据库
        chroma_client = chromadb.PersistentClient(
            path="../chroma_db",
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        print("✅ ChromaDB 客户端连接成功")

        # 获取现有集合
        collection = chroma_client.get_collection("persona_memories")
        print("✅ 获取 persona_memories 集合成功")

        # 检查集合内容
        count = collection.count()
        print(f"📊 集合中记忆数量: {count}")

        if count > 0:
            # 获取所有记忆
            all_memories = collection.get(
                include=['metadatas', 'documents', 'embeddings']
            )

            print(f"📄 获取到 {len(all_memories['ids'])} 条记忆")

            # 显示前几条记忆
            for i in range(min(3, len(all_memories['ids']))):
                metadata = all_memories['metadatas'][i]
                content = all_memories['documents'][i]
                print(f"  {i+1}. {metadata.get('title', 'N/A')}")
                print(f"     内容: {content[:50]}...")
                print(f"     分享等级: {metadata.get('sharing_level', 'N/A')}")

            # 测试向量搜索（使用第一个记忆的嵌入作为查询）
            if (all_memories['embeddings'] and
                len(all_memories['embeddings']) > 0 and
                all_memories['embeddings'][0] is not None):
                print(f"\n🔍 测试向量搜索...")
                test_embedding = all_memories['embeddings'][0]
                print(f"测试嵌入维度: {len(test_embedding)}")

                # 执行向量查询
                search_results = collection.query(
                    query_embeddings=[test_embedding],
                    n_results=3,
                    where={"sharing_level": {"$lte": 5}}
                )

                print(f"搜索结果数量: {len(search_results['ids'][0]) if search_results['ids'] else 0}")

                if search_results['distances'] and search_results['distances'][0]:
                    print("搜索结果:")
                    for i, (doc, distance) in enumerate(zip(
                        search_results['documents'][0][:3],
                        search_results['distances'][0][:3]
                    )):
                        similarity = max(0.0, 1.0 - distance / 2.0)
                        print(f"  {i+1}. 距离: {distance:.3f}, 相似度: {similarity:.3f}")
                        print(f"     内容: {doc[:50]}...")

                # 测试不同的相似度阈值
                print(f"\n🔍 测试相似度阈值...")
                thresholds = [0.1, 0.3, 0.5, 0.7]
                for threshold in thresholds:
                    filtered_count = 0
                    if search_results['distances'] and search_results['distances'][0]:
                        for distance in search_results['distances'][0]:
                            similarity = max(0.0, 1.0 - distance / 2.0)
                            if similarity >= threshold:
                                filtered_count += 1
                    print(f"  阈值 {threshold}: {filtered_count} 条结果")

            else:
                print("⚠️ 没有嵌入数据")

        else:
            print("⚠️ 集合为空")

        print("\n✅ 简单搜索测试完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_search_test()

#!/usr/bin/env python3
"""
测试增强的相似度计算
"""

import requests
import json

def test_enhanced_similarity():
    """测试增强的相似度计算"""
    print("🔍 测试增强的相似度计算...")
    
    base_url = "http://localhost:8082"
    
    # 测试不同查询的相似度分布
    test_queries = ["咖啡", "画画", "音乐", "展览"]
    
    for query in test_queries:
        print(f"\n🔍 搜索: '{query}'")
        try:
            response = requests.get(f'{base_url}/api/memories/persona?query={query}')
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    memories = data['memories']
                    
                    if memories:
                        similarities = [m.get('similarity', 0) for m in memories]
                        
                        print(f"   📊 相似度分布:")
                        print(f"      最高: {max(similarities):.3f}")
                        print(f"      最低: {min(similarities):.3f}")
                        print(f"      差值: {max(similarities) - min(similarities):.3f}")
                        
                        print(f"   🎯 前3个结果:")
                        for i, memory in enumerate(memories[:3]):
                            similarity = memory.get('similarity', 0)
                            title = memory['title']
                            print(f"      {i+1}. {title} ({similarity:.3f})")
                    else:
                        print(f"   ❌ 没有找到相关记忆")
                else:
                    print(f"   ❌ 搜索失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    print(f"\n🎉 增强相似度测试完成！")
    print(f"\n📋 平方函数增强效果:")
    print(f"1. 🔢 使用 (1-d/2)² 替代 (1-d/2)")
    print(f"2. 📈 增强高相似度和低相似度的区分")
    print(f"3. 🎯 突出真正相关的记忆")
    print(f"4. 📊 提供更大的相似度分布范围")

if __name__ == "__main__":
    test_enhanced_similarity()

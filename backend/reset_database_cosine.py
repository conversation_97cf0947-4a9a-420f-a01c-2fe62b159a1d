#!/usr/bin/env python3
"""
重置数据库以使用余弦距离
"""

import shutil
import os

def reset_database_for_cosine():
    """重置数据库以使用余弦距离"""
    print("🔄 重置数据库以使用余弦距离...")
    
    # 删除现有的 ChromaDB 数据库
    chroma_db_path = "../chroma_db"
    if os.path.exists(chroma_db_path):
        print(f"🗑️ 删除现有数据库: {chroma_db_path}")
        shutil.rmtree(chroma_db_path)
        print("✅ 数据库已删除")
    else:
        print("ℹ️ 数据库目录不存在，无需删除")
    
    print("✅ 数据库重置完成！")
    print("📝 下次启动应用程序时，将使用余弦距离创建新的集合")

if __name__ == "__main__":
    reset_database_for_cosine()

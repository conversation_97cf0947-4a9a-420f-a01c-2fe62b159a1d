"""
嵌入客户端模块
使用火山引擎 doubao-embedding 模型
"""

import logging
from typing import List, Union
from config import Config

logger = logging.getLogger(__name__)


class EmbeddingClient:
    """火山引擎嵌入客户端"""

    def __init__(self):
        """初始化嵌入客户端"""
        self.config = Config.VECTOR_DB_CONFIG
        self.model_name = self.config.get('embedding_model', 'doubao-embedding-text-240715')
        self.embedding_dim = None
        
        # 初始化火山引擎客户端
        self._init_client()

    def _init_client(self):
        """初始化火山引擎客户端"""
        try:
            from volcenginesdkarkruntime import Ark

            self.client = Ark(
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key=Config.VOLCENGINE_ACCESS_KEY
            )

            # 测试连接并获取嵌入维度
            test_response = self.client.embeddings.create(
                model=self.model_name,
                input=["测试连接"]
            )

            if test_response and hasattr(test_response, 'data') and test_response.data:
                self.embedding_dim = len(test_response.data[0].embedding)
                logger.info(f"✅ 火山引擎嵌入客户端初始化成功: {self.model_name}")
                logger.info(f"📏 嵌入维度: {self.embedding_dim}")
            else:
                raise Exception("测试响应格式不正确")

        except ImportError:
            logger.error("❌ volcenginesdkarkruntime 未安装，请运行: pip install volcenginesdkarkruntime")
            raise
        except Exception as e:
            logger.error(f"❌ 火山引擎嵌入客户端初始化失败: {e}")
            logger.error("💡 请检查火山引擎API配置 (VOLC_ACCESSKEY, VOLC_SECRETKEY)")
            raise

    def encode(self, texts: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
        """
        生成文本嵌入

        Args:
            texts: 单个文本或文本列表

        Returns:
            嵌入向量或嵌入向量列表
        """
        # 统一处理输入格式
        if isinstance(texts, str):
            texts = [texts]
            return_single = True
        else:
            return_single = False

        try:
            # 批量处理，火山引擎支持批量嵌入
            response = self.client.embeddings.create(
                model=self.model_name,
                input=texts
            )

            if response and hasattr(response, 'data') and response.data:
                embeddings = []
                for item in response.data:
                    embeddings.append(item.embedding)

                logger.debug(f"🔥 火山引擎嵌入生成成功 - 文本数: {len(texts)}, 维度: {len(embeddings[0])}")
                
                # 根据输入格式返回结果
                if return_single:
                    return embeddings[0]
                else:
                    return embeddings
            else:
                raise Exception("火山引擎响应格式不正确")

        except Exception as e:
            logger.error(f"💥 文本嵌入生成失败: {e}")
            raise

    def get_embedding_dim(self) -> int:
        """获取嵌入维度"""
        return self.embedding_dim

    def get_model_info(self) -> dict:
        """获取模型信息"""
        return {
            'provider': 'volcengine',
            'model_name': self.model_name,
            'embedding_dim': self.embedding_dim
        }


# 全局嵌入客户端实例
_embedding_client = None


def get_embedding_client() -> EmbeddingClient:
    """获取全局嵌入客户端实例"""
    global _embedding_client
    if _embedding_client is None:
        _embedding_client = EmbeddingClient()
    return _embedding_client


def create_embeddings(texts: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
    """
    便捷函数：生成文本嵌入

    Args:
        texts: 单个文本或文本列表

    Returns:
        嵌入向量或嵌入向量列表
    """
    client = get_embedding_client()
    return client.encode(texts)

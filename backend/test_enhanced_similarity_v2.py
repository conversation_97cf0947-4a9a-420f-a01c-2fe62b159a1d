#!/usr/bin/env python3
"""
测试强化相似度计算 v2
"""

import requests
import json
import math

def calculate_similarity_examples():
    """计算相似度示例"""
    print("🔢 强化相似度计算公式示例:")
    print("公式: similarity = (base_similarity^4) * (2^base_similarity)")
    print("其中 base_similarity = 1 - distance/2")
    print()
    
    distances = [0.189, 0.214, 0.235, 0.240, 0.291, 0.306]
    
    for distance in distances:
        normalized_distance = distance / 2.0
        base_similarity = 1.0 - normalized_distance
        similarity = max(0.0, (base_similarity ** 4) * (2.0 ** base_similarity))
        print(f"距离 {distance:.3f} → 基础相似度 {base_similarity:.3f} → 强化相似度 {similarity:.6f}")
    
    print()

def test_enhanced_similarity_v2():
    """测试强化相似度计算 v2"""
    print("🚀 测试强化相似度计算 v2...")
    
    # 先显示计算公式示例
    calculate_similarity_examples()
    
    base_url = "http://localhost:8082"
    
    # 测试不同查询的相似度分布
    test_queries = ["咖啡", "画画", "音乐", "展览"]
    
    for query in test_queries:
        print(f"\n🔍 搜索: '{query}'")
        try:
            response = requests.get(f'{base_url}/api/memories/persona?query={query}')
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    memories = data['memories']
                    
                    if memories:
                        similarities = [m.get('similarity', 0) for m in memories]
                        
                        print(f"   📊 相似度分布:")
                        print(f"      最高: {max(similarities):.6f}")
                        print(f"      最低: {min(similarities):.6f}")
                        print(f"      差值: {max(similarities) - min(similarities):.6f}")
                        print(f"      比率: {max(similarities) / min(similarities) if min(similarities) > 0 else 'N/A':.2f}")
                        
                        print(f"   🎯 前5个结果:")
                        for i, memory in enumerate(memories[:5]):
                            similarity = memory.get('similarity', 0)
                            title = memory['title']
                            print(f"      {i+1}. {title} ({similarity:.6f})")
                        
                        # 分析区分度
                        if len(similarities) > 1:
                            # 计算相似度的标准差
                            mean_sim = sum(similarities) / len(similarities)
                            variance = sum((s - mean_sim) ** 2 for s in similarities) / len(similarities)
                            std_dev = variance ** 0.5
                            
                            print(f"   📊 区分度分析:")
                            print(f"      标准差: {std_dev:.6f}")
                            print(f"      变异系数: {std_dev / mean_sim * 100:.2f}%")
                            
                            if std_dev > 0.1:
                                print(f"      ✅ 区分度优秀")
                            elif std_dev > 0.05:
                                print(f"      ✅ 区分度良好")
                            elif std_dev > 0.01:
                                print(f"      ⚠️ 区分度一般")
                            else:
                                print(f"      ❌ 区分度较差")
                    else:
                        print(f"   ❌ 没有找到相关记忆")
                else:
                    print(f"   ❌ 搜索失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    print(f"\n🎉 强化相似度测试 v2 完成！")
    print(f"\n📋 强化效果说明:")
    print(f"1. 🔢 使用 (base_similarity^4) * (2^base_similarity)")
    print(f"2. 📈 四次方函数 + 指数函数双重增强")
    print(f"3. 🎯 极大地突出高相似度记忆")
    print(f"4. 📊 提供极大的相似度分布范围")
    print(f"5. 🔍 更精确的相关性排序")

if __name__ == "__main__":
    test_enhanced_similarity_v2()

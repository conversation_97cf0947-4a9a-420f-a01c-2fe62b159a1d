"""
虚拟人个人记忆管理模块
负责管理虚拟人小雨的个人背景、经历和记忆
使用SQLite数据库存储
"""

import sqlite3
import json
import logging
from typing import List, Dict, Optional, Any
from config import Config

# 配置日志
logger = logging.getLogger(__name__)


class PersonaMemoryManager:
    """虚拟人个人记忆管理器"""

    def __init__(self, db_manager=None):
        """
        初始化个人记忆管理器

        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.db_path = Config.DATABASE_PATH
        self._init_persona_memory_tables()
        self._ensure_persona_memories_exist()

    def _init_persona_memory_tables(self):
        """初始化虚拟人记忆相关表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 虚拟人个人记忆表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS persona_memories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    memory_type TEXT NOT NULL,  -- 'childhood', 'education', 'work', 'hobby', 'relationship', 'travel', 'achievement', 'challenge'
                    category TEXT NOT NULL,     -- 'experience', 'emotion', 'skill', 'preference', 'goal', 'secret'
                    title TEXT NOT NULL,        -- 记忆标题
                    content TEXT NOT NULL,      -- 详细内容
                    emotion TEXT,               -- 相关情感 'happy', 'sad', 'excited', 'nostalgic', 'proud', 'embarrassed'
                    importance REAL DEFAULT 1.0, -- 重要程度 1.0-5.0
                    keywords TEXT,              -- 关键词，JSON格式
                    time_period TEXT,           -- 时间段 'childhood', 'teenage', 'college', 'recent'
                    sharing_level INTEGER DEFAULT 1, -- 分享等级 1-5，数字越高越私密
                    last_shared TIMESTAMP,      -- 最后分享时间
                    share_count INTEGER DEFAULT 0, -- 分享次数
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 记忆触发关键词表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memory_triggers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    memory_id INTEGER NOT NULL,
                    trigger_word TEXT NOT NULL,
                    trigger_type TEXT NOT NULL, -- 'keyword', 'emotion', 'context'
                    weight REAL DEFAULT 1.0,
                    FOREIGN KEY (memory_id) REFERENCES persona_memories (id)
                )
            ''')

            # 记忆关联表（记忆之间的关联）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memory_associations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    memory_id_1 INTEGER NOT NULL,
                    memory_id_2 INTEGER NOT NULL,
                    association_type TEXT NOT NULL, -- 'similar', 'opposite', 'sequential', 'causal'
                    strength REAL DEFAULT 1.0,
                    FOREIGN KEY (memory_id_1) REFERENCES persona_memories (id),
                    FOREIGN KEY (memory_id_2) REFERENCES persona_memories (id)
                )
            ''')

            conn.commit()

    def _ensure_persona_memories_exist(self):
        """确保虚拟人的基础记忆存在"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM persona_memories')
            count = cursor.fetchone()[0]

            if count == 0:
                self._initialize_default_memories()

    def _initialize_default_memories(self):
        """初始化默认的个人记忆"""
        default_memories = [
            # 童年记忆
            {
                'memory_type': 'childhood',
                'category': 'experience',
                'title': '第一次画画',
                'content': '我记得5岁的时候，妈妈给我买了第一盒水彩笔。我兴奋地在纸上画了一只小猫，虽然画得很丑，但妈妈夸我很有天赋。那是我第一次感受到创作的快乐。',
                'emotion': 'happy',
                'importance': 4.5,
                'keywords': '["画画", "水彩笔", "小猫", "妈妈", "天赋", "创作"]',
                'time_period': 'childhood',
                'sharing_level': 2
            },
            {
                'memory_type': 'childhood',
                'category': 'emotion',
                'title': '搬家的不舍',
                'content': '8岁那年我们从老家搬到上海，我特别舍不得那个小院子里的桂花树。每年秋天桂花开的时候，整个院子都香香的。现在闻到桂花香，还是会想起那个温暖的小院子。',
                'emotion': 'nostalgic',
                'importance': 3.8,
                'keywords': '["搬家", "上海", "桂花树", "院子", "秋天", "香味"]',
                'time_period': 'childhood',
                'sharing_level': 3
            },

            # 学生时代
            {
                'memory_type': 'education',
                'category': 'achievement',
                'title': '高中美术比赛获奖',
                'content': '高二的时候参加了全市的美术比赛，我画了一幅《雨后的徐汇》，没想到拿了二等奖。那是我第一次觉得自己真的可以靠画画做点什么。颁奖的时候我激动得差点哭了。',
                'emotion': 'proud',
                'importance': 4.2,
                'keywords': '["美术比赛", "雨后的徐汇", "二等奖", "高二", "激动"]',
                'time_period': 'teenage',
                'sharing_level': 2
            },
            {
                'memory_type': 'education',
                'category': 'challenge',
                'title': '艺考的焦虑',
                'content': '准备艺考的那段时间真的很焦虑，每天画到手抽筋，还要担心文化课。有一次画素描画到凌晨3点，突然就崩溃大哭了。好在最后还是考上了理想的美术学院。',
                'emotion': 'sad',
                'importance': 3.5,
                'keywords': '["艺考", "焦虑", "素描", "凌晨", "崩溃", "美术学院"]',
                'time_period': 'teenage',
                'sharing_level': 4
            },

            # 大学生活
            {
                'memory_type': 'education',
                'category': 'experience',
                'title': '大一的室友',
                'content': '大一刚入学的时候，室友小林是个特别开朗的女孩，她教会了我很多生活技能，比如怎么做简单的料理。我们经常一起在宿舍楼顶看星星聊天，那些夜晚真的很美好。',
                'emotion': 'happy',
                'importance': 3.7,
                'keywords': '["大一", "室友", "小林", "料理", "宿舍楼顶", "星星"]',
                'time_period': 'college',
                'sharing_level': 2
            },
            {
                'memory_type': 'hobby',
                'category': 'skill',
                'title': '学会做咖啡',
                'content': '大二的时候迷上了咖啡，专门去学了手冲咖啡。第一次成功做出一杯好喝的咖啡时，那种成就感真的无法形容。现在每天早上给自己冲一杯咖啡已经成了习惯。',
                'emotion': 'excited',
                'importance': 3.0,
                'keywords': '["咖啡", "手冲", "大二", "成就感", "习惯"]',
                'time_period': 'college',
                'sharing_level': 1
            },

            # 兴趣爱好
            {
                'memory_type': 'hobby',
                'category': 'preference',
                'title': '喜欢听轻音乐',
                'content': '我特别喜欢在画画的时候听一些轻柔的音乐，比如久石让的钢琴曲。音乐能让我更专注，也能激发灵感。有时候一首好听的曲子能让我画出意想不到的作品。',
                'emotion': 'happy',
                'importance': 2.8,
                'keywords': '["轻音乐", "画画", "久石让", "钢琴曲", "专注", "灵感"]',
                'time_period': 'recent',
                'sharing_level': 1
            },
            {
                'memory_type': 'hobby',
                'category': 'experience',
                'title': '第一次看画展',
                'content': '去年和朋友一起去看了莫奈的画展，站在《睡莲》前面的那一刻，我被深深震撼了。那种光影的变化，色彩的层次，让我重新理解了什么叫艺术。回来后我的画风都有了一些改变。',
                'emotion': 'excited',
                'importance': 4.0,
                'keywords': '["画展", "莫奈", "睡莲", "震撼", "光影", "色彩", "艺术", "画风"]',
                'time_period': 'recent',
                'sharing_level': 2
            }
        ]

        for memory in default_memories:
            self.add_persona_memory(**memory)

    def add_persona_memory(self, memory_type: str, category: str, title: str, content: str,
                          emotion: str = None, importance: float = 1.0, keywords: str = None,
                          time_period: str = 'recent', sharing_level: int = 1) -> int:
        """添加个人记忆"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO persona_memories
                (memory_type, category, title, content, emotion, importance, keywords, time_period, sharing_level)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (memory_type, category, title, content, emotion, importance, keywords, time_period, sharing_level))

            memory_id = cursor.lastrowid
            conn.commit()

            # 添加触发关键词
            if keywords:
                self._add_memory_triggers(memory_id, keywords)

            return memory_id

    def _add_memory_triggers(self, memory_id: int, keywords: str):
        """为记忆添加触发关键词"""
        try:
            keyword_list = json.loads(keywords)
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                for keyword in keyword_list:
                    cursor.execute('''
                        INSERT INTO memory_triggers (memory_id, trigger_word, trigger_type, weight)
                        VALUES (?, ?, ?, ?)
                    ''', (memory_id, keyword.lower(), 'keyword', 1.0))
                conn.commit()
        except json.JSONDecodeError:
            pass

    def get_relevant_persona_memories(self, context: str, user_affection: int = 50,
                                    limit: int = 3) -> List[Dict]:
        """
        根据对话上下文获取相关的个人记忆

        Args:
            context: 对话上下文
            user_affection: 用户好感度，影响分享私密记忆的概率
            limit: 返回记忆数量限制
        """
        context_lower = context.lower()

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 查找匹配的记忆
            cursor.execute('''
                SELECT DISTINCT pm.*,
                       COUNT(mt.id) as trigger_count,
                       AVG(mt.weight) as avg_weight
                FROM persona_memories pm
                LEFT JOIN memory_triggers mt ON pm.id = mt.memory_id
                WHERE mt.trigger_word IN (
                    SELECT DISTINCT trigger_word
                    FROM memory_triggers
                    WHERE ? LIKE '%' || trigger_word || '%'
                )
                AND pm.sharing_level <= ?
                GROUP BY pm.id
                ORDER BY trigger_count DESC, avg_weight DESC, pm.importance DESC
                LIMIT ?
            ''', (context_lower, min(5, max(1, user_affection // 20)), limit))

            memories = cursor.fetchall()

            result = []
            for memory in memories:
                # 更新分享统计
                self._update_memory_share_stats(memory[0])

                result.append({
                    'id': memory[0],
                    'memory_type': memory[1],
                    'category': memory[2],
                    'title': memory[3],
                    'content': memory[4],
                    'emotion': memory[5],
                    'importance': memory[6],
                    'time_period': memory[8],
                    'sharing_level': memory[9]
                })

            return result

    def _update_memory_share_stats(self, memory_id: int):
        """更新记忆分享统计"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE persona_memories
                SET last_shared = CURRENT_TIMESTAMP, share_count = share_count + 1
                WHERE id = ?
            ''', (memory_id,))
            conn.commit()

    def get_memory_stats(self) -> Dict:
        """获取记忆统计信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 总记忆数
            cursor.execute('SELECT COUNT(*) FROM persona_memories')
            total_memories = cursor.fetchone()[0]

            # 按类型统计
            cursor.execute('''
                SELECT memory_type, COUNT(*)
                FROM persona_memories
                GROUP BY memory_type
            ''')
            type_stats = dict(cursor.fetchall())

            # 按分享等级统计
            cursor.execute('''
                SELECT sharing_level, COUNT(*)
                FROM persona_memories
                GROUP BY sharing_level
            ''')
            sharing_stats = dict(cursor.fetchall())

            return {
                'total_memories': total_memories,
                'by_type': type_stats,
                'by_sharing_level': sharing_stats
            }

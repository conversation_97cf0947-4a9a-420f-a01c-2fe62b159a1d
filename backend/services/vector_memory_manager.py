"""
向量记忆管理模块
使用Chroma向量数据库进行语义记忆存储和检索
"""

import json
import logging
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from services.prompt_manager import PromptManager
from models.vector_database import UserMemoryVectorDB
from config import Config

# 配置日志
logger = logging.getLogger(__name__)

class VectorMemoryManager:
    """基于向量数据库的记忆管理器"""

    def __init__(self, llm_service=None, persist_directory: str = None):
        """
        初始化向量记忆管理器

        Args:
            llm_service: LLM服务实例，用于智能记忆提取
            persist_directory: Chroma数据库持久化目录
        """
        self.llm_service = llm_service
        self.prompt_manager = PromptManager()

        # 使用配置中的设置
        self.config = Config.VECTOR_DB_CONFIG

        # 初始化向量数据库
        try:
            self.vector_db = UserMemoryVectorDB(persist_directory)
            logger.info("✅ 向量记忆管理器初始化成功")
        except Exception as e:
            logger.error(f"❌ 向量记忆管理器初始化失败: {e}")
            raise

        # 记忆类型定义
        self.memory_types = {
            'personal': '个人信息',
            'preference': '喜好偏好',
            'experience': '经历体验',
            'goal': '目标计划',
            'relationship': '关系状态',
            'emotion': '情感状态'
        }

        # 重要性等级
        self.importance_levels = {
            'low': 1.0,
            'medium': 2.0,
            'high': 3.0
        }



    def extract_memories_from_text(self, user_id: str, text: str) -> List[Dict]:
        """
        从文本中提取记忆信息

        Args:
            user_id: 用户ID
            text: 输入文本

        Returns:
            提取的记忆信息列表
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        logger.info(f"🧠 开始向量记忆提取 [{request_id}] - 用户: {user_id}")
        logger.info(f"📝 提取文本: {text}")

        memories = []

        # 如果有LLM服务，使用智能提取
        if self.llm_service:
            try:
                memories = self._extract_memories_with_llm(text, request_id)
            except Exception as e:
                logger.error(f"💥 LLM记忆提取失败 [{request_id}]: {str(e)}")
                # 降级到规则提取
                memories = self._extract_memories_with_rules(text, request_id)
        else:
            # 使用规则提取
            memories = self._extract_memories_with_rules(text, request_id)

        # 保存提取的记忆到向量数据库
        saved_count = 0
        for memory in memories:
            if self._is_valid_memory(memory):
                self.vector_db.save_memory(
                    user_id=user_id,
                    memory_type=memory.get('type', 'experience'),
                    content=memory.get('content', ''),
                    importance=float(memory.get('importance', 1.0))
                )
                saved_count += 1

        logger.info(f"✅ 向量记忆提取完成 [{request_id}] - 提取: {len(memories)} 条, 保存: {saved_count} 条")
        return memories

    def _extract_memories_with_llm(self, text: str, request_id: str) -> List[Dict]:
        """使用LLM进行智能记忆提取"""
        # 使用提示词模块获取记忆提取提示词
        system_prompt = self.prompt_manager.render_template('memory_extraction.j2')

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请从这段话中提取记忆信息：{text}"}
        ]

        # 调用LLM服务
        if hasattr(self.llm_service, 'client') and hasattr(self.llm_service, 'model'):
            # 真实的LLM服务
            result = self.llm_service.client.chat_completions(
                messages=messages,
                model=self.llm_service.model,
                temperature=0.3,
                max_tokens=300
            )
        else:
            # 模拟LLM服务，直接返回空结果
            result = {"success": False, "error": "模拟LLM服务不支持记忆提取"}

        if result["success"]:
            content = result["content"]
            logger.info(f"📄 LLM原始响应: {content}")

            try:
                memories = json.loads(content)
                if isinstance(memories, list):
                    logger.info(f"✅ LLM记忆提取成功 [{request_id}] - {len(memories)} 条")
                    for i, memory in enumerate(memories):
                        logger.info(f"   {i+1}. [{memory.get('type', 'unknown')}] {memory.get('content', '')}")
                    return memories
                else:
                    logger.warning(f"⚠️  返回格式不是列表，使用空列表")
                    return []
            except json.JSONDecodeError:
                logger.warning(f"⚠️  JSON解析失败，尝试清理内容")
                try:
                    # 移除可能的markdown代码块格式
                    cleaned_content = re.sub(r'```(?:json)?\s*([\s\S]*?)\s*```', r'\1', content)
                    memories = json.loads(cleaned_content)
                    if isinstance(memories, list):
                        logger.info(f"✅ 清理后解析成功")
                        return memories
                    else:
                        return []
                except:
                    logger.warning(f"⚠️  清理后仍解析失败，使用空列表")
                    return []
        else:
            logger.error(f"❌ LLM记忆提取API错误: {result.get('error', '未知错误')}")
            return []

    def _extract_memories_with_rules(self, text: str, request_id: str) -> List[Dict]:
        """使用规则进行记忆提取"""
        logger.info(f"🔧 使用规则提取记忆 [{request_id}]")

        memories = []
        text_lower = text.lower()

        # 个人信息提取
        if any(word in text_lower for word in ['我叫', '我是', '我的名字', '我来自', '我住在']):
            memories.append({
                "type": "personal",
                "content": f"用户提到了个人身份信息: {text[:50]}...",
                "importance": "2.5"
            })

        # 喜好偏好提取
        if any(word in text_lower for word in ['喜欢', '爱好', '兴趣', '偏爱', '热爱', '讨厌', '不喜欢']):
            memories.append({
                "type": "preference",
                "content": f"用户分享了喜好偏好: {text[:50]}...",
                "importance": "2.0"
            })

        # 经历体验提取
        if any(word in text_lower for word in ['昨天', '今天', '最近', '之前', '经历', '去了', '做了', '参加了']):
            memories.append({
                "type": "experience",
                "content": f"用户分享了个人经历: {text[:50]}...",
                "importance": "1.5"
            })

        # 目标计划提取
        if any(word in text_lower for word in ['想要', '计划', '目标', '希望', '打算', '准备']):
            memories.append({
                "type": "goal",
                "content": f"用户提到了目标计划: {text[:50]}...",
                "importance": "2.0"
            })

        # 情感状态提取
        if any(word in text_lower for word in ['开心', '难过', '生气', '焦虑', '兴奋', '失望', '满意']):
            memories.append({
                "type": "emotion",
                "content": f"用户表达了情感状态: {text[:50]}...",
                "importance": "1.5"
            })

        logger.info(f"🔧 规则提取完成 [{request_id}] - {len(memories)} 条")
        return memories

    def _is_valid_memory(self, memory: Dict) -> bool:
        """验证记忆是否有效"""
        if not isinstance(memory, dict):
            return False

        # 检查必要字段
        if not memory.get('content') or not memory.get('type'):
            return False

        # 检查记忆类型是否有效
        if memory.get('type') not in self.memory_types:
            return False

        # 检查重要性是否在合理范围内
        try:
            importance = float(memory.get('importance', 1.0))
            if importance < 0.5 or importance > 3.0:
                return False
        except (ValueError, TypeError):
            return False

        return True



    def get_relevant_memories(self, user_id: str, query_text: str = None, keywords: List[str] = None,
                            limit: int = None, similarity_threshold: float = None) -> List[Dict]:
        """
        获取相关记忆（基于语义相似性）

        Args:
            user_id: 用户ID
            query_text: 查询文本（用于语义搜索）
            keywords: 关键词列表（备用）
            limit: 返回数量限制
            similarity_threshold: 相似度阈值

        Returns:
            相关记忆列表
        """
        return self.vector_db.get_relevant_memories(
            user_id=user_id,
            query_text=query_text,
            keywords=keywords,
            limit=limit,
            similarity_threshold=similarity_threshold
        )



    def extract_keywords(self, text: str) -> List[str]:
        """
        从文本中提取关键词用于记忆检索

        Args:
            text: 输入文本

        Returns:
            关键词列表
        """
        keywords = []

        # 常见的重要词汇
        important_words = [
            '工作', '学习', '家人', '朋友', '爱好', '电影', '音乐', '书', '旅行',
            '梦想', '目标', '困难', '开心', '难过', '压力', '放松', '运动', '美食',
            '学校', '公司', '家', '咖啡', '画画', '艺术', '设计', '创作', '灵感'
        ]

        for word in important_words:
            if word in text:
                keywords.append(word)

        # 使用简单的分词提取更多关键词
        words = re.findall(r'[\u4e00-\u9fff]+', text)  # 提取中文词汇
        for word in words:
            if len(word) >= 2 and word not in keywords:
                keywords.append(word)

        return keywords[:8]  # 最多返回8个关键词

    def get_memory_summary(self, user_id: str) -> Dict:
        """
        获取用户记忆摘要

        Args:
            user_id: 用户ID

        Returns:
            记忆摘要信息
        """
        # 委托给向量数据库的统计方法
        stats = self.vector_db.get_memory_statistics()

        # 过滤出特定用户的统计信息
        user_stats = stats.get('by_user', {}).get(user_id, 0)

        return {
            'total_memories': user_stats,
            'type_distribution': stats.get('by_type', {}),
            'average_importance': stats.get('avg_importance', 0),
            'most_common_type': max(stats.get('by_type', {}).items(), key=lambda x: x[1])[0] if stats.get('by_type') else None,
            'total_recalls': 0  # 这个信息需要从向量数据库获取
        }



    def search_memories(self, user_id: str, search_query: str, limit: int = 10) -> List[Dict]:
        """
        搜索用户记忆

        Args:
            user_id: 用户ID
            search_query: 搜索查询
            limit: 返回数量限制

        Returns:
            搜索结果列表
        """
        return self.vector_db.search_memories(user_id, search_query, limit)

    def get_memory_statistics(self) -> Dict:
        """
        获取整体记忆统计信息

        Returns:
            统计信息字典
        """
        return self.vector_db.get_memory_statistics()

#!/usr/bin/env python3
"""
测试余弦距离计算
"""

import requests
import json

def test_cosine_distance():
    """测试余弦距离计算"""
    print("🔄 测试余弦距离计算...")

    base_url = "http://localhost:8082"

    # 首先删除现有集合以强制重新创建（使用余弦距离）
    print("\n🗑️ 重新创建集合以使用余弦距离...")

    # 测试分页获取记忆
    print("\n📄 测试分页获取记忆...")
    try:
        response = requests.get(f'{base_url}/api/memories/persona?page=1&page_size=3')
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                memories = data['memories']
                pagination = data['pagination']
                print(f"✅ 分页获取成功: {len(memories)} 条记忆")
                print(f"📊 分页信息: 第{pagination['page']}页，共{pagination['total']}条")

                for i, memory in enumerate(memories):
                    print(f"  {i+1}. {memory['title']}")
                    print(f"     {memory['content'][:50]}...")
            else:
                print(f"❌ 分页获取失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 分页测试失败: {e}")

    # 测试搜索功能
    print("\n🔍 测试余弦距离搜索功能...")
    search_queries = ["画画", "艺术", "展览", "音乐", "咖啡"]

    for query in search_queries:
        try:
            print(f"\n搜索: '{query}'")
            response = requests.get(f'{base_url}/api/memories/persona?query={query}')
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    memories = data['memories']
                    print(f"✅ 搜索到 {len(memories)} 条记忆")

                    for i, memory in enumerate(memories[:3]):
                        similarity = memory.get('similarity', 0)
                        print(f"  {i+1}. {memory['title']} (相似度: {similarity:.3f})")
                        print(f"     {memory['content'][:50]}...")
                else:
                    print(f"❌ 搜索失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 搜索测试失败: {e}")

    print("\n🎉 余弦距离测试完成！")
    print("\n📋 余弦距离优势:")
    print("1. ✅ 更适合文本嵌入的相似性计算")
    print("2. ✅ 距离范围固定在0-2之间")
    print("3. ✅ 相似度计算更直观（0=完全相似，1=完全不相似）")
    print("4. ✅ 对向量长度不敏感，只关注方向")

if __name__ == "__main__":
    test_cosine_distance()

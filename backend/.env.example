# 火山引擎配置
VOLCENGINE_ACCESS_KEY=your_access_key_here
VOLCENGINE_SECRET_KEY=your_secret_key_here
VOLCENGINE_REGION=cn-beijing
VOLCENGINE_MODEL=doubao-lite-4k

# 应用配置
FLASK_ENV=development
FLASK_DEBUG=True

# 消息分段配置
# 是否使用LLM进行智能分段（true/false）
# true: 使用LLM分段，更自然但消耗token
# false: 使用备用分段，节约token但效果一般
USE_LLM_SPLIT=false

# Memobase记忆系统配置
# Memobase项目URL（本地部署或云服务）
MEMOBASE_PROJECT_URL=http://localhost:8019
# Memobase API密钥（本地默认为secret，云服务需要申请）
MEMOBASE_API_KEY=secret
# 上下文最大token数
MEMOBASE_MAX_TOKEN_SIZE=500
# 是否自动刷新记忆（true/false）
MEMOBASE_AUTO_FLUSH=true

# 向量数据库配置（已弃用，保留用于兼容性）
# 是否使用向量数据库进行记忆存储（true/false）
# 已切换到Memobase，此配置已无效
USE_VECTOR_DB=false

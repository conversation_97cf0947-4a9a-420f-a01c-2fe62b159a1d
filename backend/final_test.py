#!/usr/bin/env python3
"""
最终测试脚本
"""

import requests
import json

def test_final():
    """最终测试"""
    print("🎉 最终测试开始...")
    
    base_url = "http://localhost:8081"
    
    # 测试分页获取记忆
    print("\n📄 测试分页获取记忆...")
    try:
        response = requests.get(f'{base_url}/api/memories/persona?page=1&page_size=3')
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                memories = data['memories']
                pagination = data['pagination']
                print(f"✅ 分页获取成功: {len(memories)} 条记忆")
                print(f"📊 分页信息: 第{pagination['page']}页，共{pagination['total']}条")
                
                for i, memory in enumerate(memories):
                    print(f"  {i+1}. {memory['title']}")
                    print(f"     {memory['content'][:50]}...")
            else:
                print(f"❌ 分页获取失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 分页测试失败: {e}")
    
    # 测试搜索功能
    print("\n🔍 测试搜索功能...")
    search_queries = ["画画", "音乐", "咖啡"]
    
    for query in search_queries:
        try:
            print(f"\n搜索: '{query}'")
            response = requests.get(f'{base_url}/api/memories/persona?query={query}')
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    memories = data['memories']
                    print(f"✅ 搜索到 {len(memories)} 条记忆")
                    
                    for i, memory in enumerate(memories[:3]):
                        similarity = memory.get('similarity', 0)
                        print(f"  {i+1}. {memory['title']} (相似度: {similarity:.6f})")
                        print(f"     {memory['content'][:50]}...")
                else:
                    print(f"❌ 搜索失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 搜索测试失败: {e}")
    
    print("\n🎉 最终测试完成！")
    print("\n📋 重构总结:")
    print("1. ✅ embedding_service 已简化并移动到 clients 包")
    print("2. ✅ 向量数据库操作已移动到 models 包")
    print("3. ✅ 记忆搜索功能正常工作")
    print("4. ✅ 分页获取记忆功能正常工作")
    print("5. ✅ 嵌入维度不匹配问题已修复")
    print("6. ✅ 相似度计算问题已修复")

if __name__ == "__main__":
    test_final()

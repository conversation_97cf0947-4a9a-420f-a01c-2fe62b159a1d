#!/usr/bin/env python3
"""
简单排序测试
"""

import requests

def simple_sort_test():
    """简单排序测试"""
    print("🔍 简单排序测试...")
    
    base_url = "http://localhost:8082"
    
    # 测试咖啡搜索
    print(f"\n搜索: '咖啡'")
    try:
        response = requests.get(f'{base_url}/api/memories/persona?query=咖啡')
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                memories = data['memories']
                print(f"✅ 搜索到 {len(memories)} 条记忆")
                
                for i, memory in enumerate(memories[:5]):
                    similarity = memory.get('similarity', 0)
                    title = memory['title']
                    print(f"  {i+1}. {title} (相似度: {similarity:.3f})")
                    
                # 检查是否按相似度排序
                similarities = [m.get('similarity', 0) for m in memories[:5]]
                is_sorted = all(similarities[i] >= similarities[i+1] for i in range(len(similarities)-1))
                if is_sorted:
                    print("✅ 结果按相似度正确排序")
                else:
                    print("❌ 结果排序有问题")
                    
            else:
                print(f"❌ 搜索失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    simple_sort_test()

#!/usr/bin/env python3
"""
测试相似度增强效果
"""

import requests
import json

def test_similarity_enhancement():
    """测试相似度增强效果"""
    print("🔍 测试相似度增强效果...")
    
    base_url = "http://localhost:8082"
    
    # 测试不同查询的相似度分布
    test_queries = [
        {
            "query": "画画",
            "description": "直接相关查询",
            "expected_high": ["第一次画画", "高中美术比赛获奖"]
        },
        {
            "query": "咖啡",
            "description": "特定主题查询",
            "expected_high": ["学会做咖啡"]
        },
        {
            "query": "音乐",
            "description": "兴趣爱好查询",
            "expected_high": ["喜欢听轻音乐"]
        },
        {
            "query": "展览",
            "description": "活动体验查询",
            "expected_high": ["第一次看画展"]
        },
        {
            "query": "学习",
            "description": "泛化概念查询",
            "expected_high": []  # 可能没有直接匹配
        }
    ]
    
    print(f"\n📊 相似度分布分析:")
    
    for i, test_case in enumerate(test_queries, 1):
        query = test_case["query"]
        description = test_case["description"]
        expected_high = test_case["expected_high"]
        
        print(f"\n{i}. {description} - 查询: '{query}'")
        
        try:
            response = requests.get(f'{base_url}/api/memories/persona?query={query}')
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    memories = data['memories']
                    
                    if memories:
                        similarities = [m.get('similarity', 0) for m in memories]
                        
                        print(f"   📈 相似度分布:")
                        print(f"      最高: {max(similarities):.3f}")
                        print(f"      最低: {min(similarities):.3f}")
                        print(f"      差值: {max(similarities) - min(similarities):.3f}")
                        print(f"      平均: {sum(similarities) / len(similarities):.3f}")
                        
                        print(f"   🎯 前5个结果:")
                        for j, memory in enumerate(memories[:5]):
                            similarity = memory.get('similarity', 0)
                            title = memory['title']
                            
                            # 检查是否是预期的高相似度结果
                            is_expected = any(exp in title for exp in expected_high)
                            marker = "⭐" if is_expected else "  "
                            
                            print(f"      {marker}{j+1}. {title} ({similarity:.3f})")
                        
                        # 分析区分度
                        if len(similarities) > 1:
                            # 计算相似度的标准差
                            mean_sim = sum(similarities) / len(similarities)
                            variance = sum((s - mean_sim) ** 2 for s in similarities) / len(similarities)
                            std_dev = variance ** 0.5
                            
                            print(f"   📊 区分度分析:")
                            print(f"      标准差: {std_dev:.3f}")
                            if std_dev > 0.05:
                                print(f"      ✅ 区分度良好")
                            elif std_dev > 0.02:
                                print(f"      ⚠️ 区分度一般")
                            else:
                                print(f"      ❌ 区分度较差")
                    else:
                        print(f"   ❌ 没有找到相关记忆")
                else:
                    print(f"   ❌ 搜索失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    print(f"\n🎉 相似度增强测试完成！")
    print(f"\n📋 增强效果说明:")
    print(f"1. 🔢 使用平方函数 (1-d/2)² 替代线性函数 (1-d/2)")
    print(f"2. 📈 增强高相似度和低相似度之间的区分")
    print(f"3. 🎯 更好地突出真正相关的记忆")
    print(f"4. 📊 提供更大的相似度分布范围")

if __name__ == "__main__":
    test_similarity_enhancement()

# 嵌入模型配置说明

## 概述

系统使用火山引擎的 **doubao-embedding-text-240715** 模型进行文本嵌入。

## 配置方式

### 安装依赖
```bash
pip install volcenginesdkarkruntime
```

### 配置环境变量
在 `backend/.env` 文件中添加火山引擎API配置：
```env
# 火山引擎API配置
VOLC_ACCESSKEY=your_access_key
VOLC_SECRETKEY=your_secret_key
VOLC_REGION=cn-beijing
```

### 配置说明
- 系统使用火山引擎的 `doubao-embedding-text-240715` 模型
- 嵌入维度：根据模型自动检测（通常为1024维）
- 支持批量嵌入，性能优异
- 专为中文优化，语义理解能力强

## 配置选项

在 `backend/config.py` 中可以调整以下配置：

```python
VECTOR_DB_CONFIG = {
    'use_vector_db': True,  # 是否使用向量数据库
    'embedding_model': 'doubao-embedding-text-240715',  # 火山引擎嵌入模型
    'similarity_threshold': 0.4,  # 相似度阈值
    'max_memories_per_query': 5,  # 每次查询返回的最大记忆数
}
```

## 使用示例

### 基本使用
```python
from clients.embedding_client import get_embedding_client, create_embeddings

# 获取嵌入客户端
client = get_embedding_client()

# 生成单个文本嵌入
embedding = client.encode("我喜欢画画")

# 生成批量文本嵌入
embeddings = client.encode(["文本1", "文本2", "文本3"])

# 便捷函数
embedding = create_embeddings("测试文本")
```

### 获取模型信息
```python
model_info = client.get_model_info()
print(f"提供商: {model_info['provider']}")  # volcengine
print(f"模型: {model_info['model_name']}")  # doubao-embedding-text-240715
print(f"维度: {model_info['embedding_dim']}")  # 1024
```

## 模型特性

| 特性 | 火山引擎 doubao-embedding |
|------|---------------------------|
| 嵌入质量 | 高 (专为中文优化) |
| 处理速度 | 快 (云端API) |
| 成本 | 按调用计费 |
| 网络依赖 | 需要 |
| 配置复杂度 | 需要API密钥 |
| 嵌入维度 | 1024维 |
| 语言支持 | 中文、英文 |

## 故障排除

### 火山引擎连接失败
1. 检查API密钥配置是否正确
2. 确认网络连接正常
3. 查看日志中的具体错误信息
4. 确认已安装 volcenginesdkarkruntime

### 嵌入维度不匹配
1. 清除向量数据库：删除 `chroma_db` 目录
2. 重启系统，让向量数据库重新初始化

### 依赖安装失败
1. 确认已安装依赖：`pip install volcenginesdkarkruntime`
2. 检查网络连接
3. 确认Python版本兼容性

## 监控和日志

系统会记录以下信息：
- 嵌入服务初始化状态
- 模型切换情况
- 嵌入生成性能
- 错误和警告信息

查看日志：
```bash
# 启动时的初始化日志
tail -f logs/app.log | grep "嵌入"

# 实时监控嵌入生成
tail -f logs/app.log | grep "Batches"
```

## 最佳实践

1. **API配置**：确保火山引擎API密钥正确配置
2. **批量处理**：尽量使用批量嵌入接口提高性能
3. **错误处理**：监控API调用失败，及时处理异常
4. **监控**：定期检查嵌入服务状态和性能指标
5. **成本控制**：合理设置相似度阈值，避免不必要的API调用

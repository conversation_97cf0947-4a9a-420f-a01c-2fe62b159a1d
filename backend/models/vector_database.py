"""
向量数据库模型
使用Chroma向量数据库进行语义存储和检索
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from config import Config
from clients.embedding_client import get_embedding_client

logger = logging.getLogger(__name__)


class VectorDatabase:
    """向量数据库基础类"""

    def __init__(self, persist_directory: str = None):
        """
        初始化向量数据库

        Args:
            persist_directory: Chroma数据库持久化目录
        """
        self.config = Config.VECTOR_DB_CONFIG
        self.persist_directory = persist_directory or self.config['persist_directory']

        # 初始化嵌入客户端
        try:
            self.embedding_client = get_embedding_client()
            model_info = self.embedding_client.get_model_info()
            logger.info(f"✅ 向量数据库嵌入客户端初始化成功: {model_info['provider']} - {model_info['model_name']}")
            logger.info(f"📏 嵌入维度: {model_info['embedding_dim']}")
        except Exception as e:
            logger.error(f"❌ 向量数据库嵌入客户端初始化失败: {e}")
            raise

        # 初始化Chroma客户端
        self._init_chroma_client()

    def _init_chroma_client(self):
        """初始化Chroma客户端"""
        try:
            import chromadb
            from chromadb.config import Settings

            # 创建持久化客户端
            self.chroma_client = chromadb.PersistentClient(
                path=self.persist_directory,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )

            logger.info(f"✅ Chroma向量数据库初始化成功，持久化目录: {self.persist_directory}")

        except ImportError:
            logger.error("❌ ChromaDB未安装，请运行: pip install chromadb")
            raise
        except Exception as e:
            logger.error(f"❌ Chroma客户端初始化失败: {e}")
            raise

    def get_or_create_collection(self, name: str, metadata: dict = None):
        """获取或创建集合，处理嵌入维度不匹配的情况"""
        try:
            # 尝试获取现有集合
            collection = self.chroma_client.get_collection(name=name)

            # 检查集合是否为空，如果不为空则检查维度
            count = collection.count()
            if count > 0:
                # 获取一个样本来检查维度
                sample = collection.get(limit=1, include=['embeddings'])
                if (sample['embeddings'] and
                    len(sample['embeddings']) > 0 and
                    sample['embeddings'][0] is not None and
                    len(sample['embeddings'][0]) > 0):
                    existing_dim = len(sample['embeddings'][0])
                    current_dim = self.embedding_client.get_embedding_dim()

                    if existing_dim != current_dim:
                        logger.warning(f"⚠️ 检测到嵌入维度不匹配: 现有={existing_dim}, 当前={current_dim}")
                        logger.warning(f"🗑️ 删除旧集合 '{name}' 并重新创建")

                        # 删除旧集合
                        self.chroma_client.delete_collection(name=name)

                        # 创建新集合，使用余弦距离
                        collection_metadata = metadata or {}
                        collection_metadata["hnsw:space"] = "cosine"
                        collection = self.chroma_client.create_collection(
                            name=name,
                            metadata=collection_metadata
                        )
                        logger.info(f"✅ 已重新创建集合 '{name}'，新维度: {current_dim}")
                    else:
                        logger.info(f"✅ 集合 '{name}' 维度匹配: {existing_dim}")
            else:
                logger.info(f"✅ 集合 '{name}' 为空，可以直接使用")

            return collection

        except Exception as e:
            # 如果集合不存在或其他错误，创建新集合
            logger.info(f"📝 创建新集合 '{name}': {e}")
            try:
                # 创建新集合，使用余弦距离
                collection_metadata = metadata or {}
                collection_metadata["hnsw:space"] = "cosine"
                return self.chroma_client.create_collection(
                    name=name,
                    metadata=collection_metadata
                )
            except Exception as create_error:
                # 如果创建失败，可能是集合已存在，尝试获取
                if "already exists" in str(create_error):
                    logger.info(f"🔄 集合 '{name}' 已存在，直接获取")
                    return self.chroma_client.get_collection(name=name)
                else:
                    raise create_error


class UserMemoryVectorDB(VectorDatabase):
    """用户记忆向量数据库"""

    def __init__(self, persist_directory: str = None):
        """初始化用户记忆向量数据库"""
        super().__init__(persist_directory)

        # 创建或获取用户记忆集合
        self.memory_collection = self.get_or_create_collection(
            name="user_memories",
            metadata={"description": "用户记忆向量存储"}
        )

    def save_memory(self, user_id: str, memory_type: str, content: str, importance: float = 1.0):
        """
        保存记忆到向量数据库

        Args:
            user_id: 用户ID
            memory_type: 记忆类型
            content: 记忆内容
            importance: 重要程度
        """
        try:
            # 生成唯一ID
            memory_id = str(uuid.uuid4())

            # 生成文本嵌入
            embedding = self.embedding_client.encode(content)

            # 准备元数据
            metadata = {
                "user_id": user_id,
                "memory_type": memory_type,
                "importance": importance,
                "created_at": datetime.now().isoformat(),
                "recall_count": 0
            }

            # 添加到向量数据库
            self.memory_collection.add(
                ids=[memory_id],
                embeddings=[embedding],
                documents=[content],
                metadatas=[metadata]
            )

            logger.info(f"💾 向量记忆已保存 - 用户: {user_id}, 类型: {memory_type}, 重要性: {importance}")

        except Exception as e:
            logger.error(f"💥 向量记忆保存失败 - 用户: {user_id}, 错误: {str(e)}")

    def get_relevant_memories(self, user_id: str, query_text: str = None, keywords: List[str] = None,
                            limit: int = None, similarity_threshold: float = None) -> List[Dict]:
        """
        获取相关记忆（基于语义相似性）

        Args:
            user_id: 用户ID
            query_text: 查询文本（用于语义搜索）
            keywords: 关键词列表（备用）
            limit: 返回数量限制
            similarity_threshold: 相似度阈值

        Returns:
            相关记忆列表
        """
        # 使用配置中的默认值
        if limit is None:
            limit = self.config['max_memories_per_query']
        if similarity_threshold is None:
            similarity_threshold = self.config['similarity_threshold']

        try:
            if query_text:
                # 使用语义搜索
                query_embedding = self.embedding_client.encode(query_text)

                # 查询向量数据库
                results = self.memory_collection.query(
                    query_embeddings=[query_embedding],
                    n_results=limit * 2,  # 获取更多结果以便过滤
                    where={"user_id": user_id}
                )

                memories = []
                if results['documents'] and results['documents'][0]:
                    for i, (doc, metadata, distance) in enumerate(zip(
                        results['documents'][0],
                        results['metadatas'][0],
                        results['distances'][0]
                    )):
                        # ChromaDB使用余弦距离，范围0-2，使用强化非线性转换增强区分度
                        # 余弦距离: 0=完全相似, 2=完全不相似
                        # 使用指数函数 + 对数缩放强化区分度
                        normalized_distance = distance / 2.0  # 归一化到0-1
                        base_similarity = 1.0 - normalized_distance
                        # 使用四次方函数 + 指数缩放大幅增强区分度
                        similarity = max(0.0, (base_similarity ** 4) * (2.0 ** base_similarity))

                        if similarity >= similarity_threshold:
                            # 更新召回次数
                            self._update_recall_count(results['ids'][0][i])

                            memories.append({
                                'content': doc,
                                'type': metadata.get('memory_type', 'unknown'),
                                'importance': metadata.get('importance', 1.0),
                                'created_at': metadata.get('created_at'),
                                'similarity': similarity,
                                'recall_count': metadata.get('recall_count', 0) + 1
                            })

                # 按重要性和相似度排序
                memories.sort(key=lambda x: (x['importance'] * x['similarity']), reverse=True)
                memories = memories[:limit]

                logger.info(f"🔍 语义搜索获取记忆 - 用户: {user_id}, 查询: {query_text[:30]}..., 结果: {len(memories)} 条")

            else:
                # 降级到关键词搜索或获取最近记忆
                memories = self._get_memories_by_keywords(user_id, keywords, limit)

            return memories

        except Exception as e:
            logger.error(f"💥 获取向量记忆失败 - 用户: {user_id}, 错误: {str(e)}")
            return []

    def _update_recall_count(self, memory_id: str):
        """更新记忆召回次数"""
        try:
            # 获取当前记忆
            result = self.memory_collection.get(ids=[memory_id])
            if result['metadatas'] and result['metadatas'][0]:
                metadata = result['metadatas'][0]
                metadata['recall_count'] = metadata.get('recall_count', 0) + 1

                # 更新记忆
                self.memory_collection.update(
                    ids=[memory_id],
                    metadatas=[metadata]
                )
        except Exception as e:
            logger.warning(f"⚠️ 更新召回次数失败: {e}")

    def _get_memories_by_keywords(self, user_id: str, keywords: List[str] = None, limit: int = 5) -> List[Dict]:
        """基于关键词获取记忆（备用方法）"""
        try:
            # 获取用户的所有记忆
            results = self.memory_collection.get(
                where={"user_id": user_id},
                limit=limit * 3  # 获取更多以便过滤
            )

            memories = []
            if results['documents']:
                for i, (doc, metadata) in enumerate(zip(results['documents'], results['metadatas'])):
                    # 如果有关键词，进行简单匹配
                    if keywords:
                        doc_lower = doc.lower()
                        if any(keyword.lower() in doc_lower for keyword in keywords):
                            memories.append({
                                'content': doc,
                                'type': metadata.get('memory_type', 'unknown'),
                                'importance': metadata.get('importance', 1.0),
                                'created_at': metadata.get('created_at'),
                                'recall_count': metadata.get('recall_count', 0)
                            })
                    else:
                        # 没有关键词时返回最重要的记忆
                        memories.append({
                            'content': doc,
                            'type': metadata.get('memory_type', 'unknown'),
                            'importance': metadata.get('importance', 1.0),
                            'created_at': metadata.get('created_at'),
                            'recall_count': metadata.get('recall_count', 0)
                        })

            # 按重要性排序
            memories.sort(key=lambda x: x['importance'], reverse=True)
            return memories[:limit]

        except Exception as e:
            logger.error(f"💥 关键词搜索记忆失败: {e}")
            return []

    def search_memories(self, user_id: str, search_query: str, limit: int = 10) -> List[Dict]:
        """
        搜索用户记忆

        Args:
            user_id: 用户ID
            search_query: 搜索查询
            limit: 返回数量限制

        Returns:
            搜索结果列表
        """
        return self.get_relevant_memories(
            user_id=user_id,
            query_text=search_query,
            limit=limit,
            similarity_threshold=0.5  # 降低阈值以获取更多结果
        )

    def get_memory_statistics(self) -> Dict:
        """获取整体记忆统计信息"""
        try:
            # 获取所有记忆
            all_memories = self.memory_collection.get()

            if not all_memories['metadatas']:
                return {
                    'total_memories': 0,
                    'by_user': {},
                    'by_type': {},
                    'avg_importance': 0.0
                }

            # 统计信息
            total_memories = len(all_memories['metadatas'])
            user_stats = {}
            type_stats = {}
            importance_sum = 0.0

            for metadata in all_memories['metadatas']:
                user_id = metadata.get('user_id', 'unknown')
                memory_type = metadata.get('memory_type', 'unknown')
                importance = metadata.get('importance', 1.0)

                # 按用户统计
                user_stats[user_id] = user_stats.get(user_id, 0) + 1

                # 按类型统计
                type_stats[memory_type] = type_stats.get(memory_type, 0) + 1

                # 重要性统计
                importance_sum += importance

            return {
                'total_memories': total_memories,
                'by_user': user_stats,
                'by_type': type_stats,
                'avg_importance': importance_sum / total_memories if total_memories > 0 else 0.0
            }

        except Exception as e:
            logger.error(f"💥 获取记忆统计失败: {e}")
            return {
                'total_memories': 0,
                'by_user': {},
                'by_type': {},
                'avg_importance': 0.0
            }


class PersonaMemoryVectorDB(VectorDatabase):
    """虚拟人个人记忆向量数据库"""

    def __init__(self, persist_directory: str = None):
        """初始化个人记忆向量数据库"""
        super().__init__(persist_directory)

        # 创建或获取个人记忆集合
        self.persona_collection = self.get_or_create_collection(
            name="persona_memories",
            metadata={"description": "虚拟人个人记忆向量存储"}
        )

    def add_persona_memory(self, memory_type: str, category: str, title: str, content: str,
                          emotion: str = None, importance: float = 1.0, keywords: str = None,
                          time_period: str = 'recent', sharing_level: int = 1) -> str:
        """添加个人记忆到向量数据库"""
        try:
            # 生成唯一ID
            memory_id = str(uuid.uuid4())

            # 生成文本嵌入（使用内容作为嵌入文本）
            embedding = self.embedding_client.encode(content)

            # 准备元数据（确保所有值都是支持的类型）
            metadata = {
                "memory_type": memory_type,
                "category": category,
                "title": title,
                "emotion": emotion or "neutral",
                "importance": float(importance),
                "time_period": time_period,
                "sharing_level": int(sharing_level),
                "keywords": keywords or "[]",
                "created_at": datetime.now().isoformat(),
                "last_shared": "",  # 使用空字符串而不是None
                "share_count": 0
            }

            # 添加到向量数据库
            self.persona_collection.add(
                ids=[memory_id],
                embeddings=[embedding],
                documents=[content],
                metadatas=[metadata]
            )

            logger.info(f"💾 个人记忆已保存 - 类型: {memory_type}, 分类: {category}, 标题: {title}")
            return memory_id

        except Exception as e:
            logger.error(f"💥 个人记忆保存失败 - 标题: {title}, 错误: {str(e)}")
            raise

    def get_relevant_persona_memories(self, query_text: str, sharing_level: int = None,
                                    memory_type: str = None, limit: int = 5,
                                    similarity_threshold: float = 0.01) -> List[Dict]:
        """
        获取相关的个人记忆

        Args:
            query_text: 查询文本
            sharing_level: 分享等级过滤
            memory_type: 记忆类型过滤
            limit: 返回数量限制
            similarity_threshold: 相似度阈值

        Returns:
            相关个人记忆列表
        """
        try:
            # 生成查询嵌入
            query_embedding = self.embedding_client.encode(query_text)

            # 构建过滤条件
            where_filter = {}
            if sharing_level is not None:
                where_filter["sharing_level"] = {"$lte": sharing_level}
            if memory_type:
                where_filter["memory_type"] = memory_type

            # 查询向量数据库
            results = self.persona_collection.query(
                query_embeddings=[query_embedding],
                n_results=limit * 2,  # 获取更多结果以便过滤
                where=where_filter if where_filter else None
            )

            memories = []
            if results['documents'] and results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    # ChromaDB使用余弦距离，范围0-2，使用强化非线性转换增强区分度
                    # 余弦距离: 0=完全相似, 2=完全不相似
                    # 使用指数函数 + 对数缩放强化区分度
                    normalized_distance = distance / 2.0  # 归一化到0-1
                    base_similarity = 1.0 - normalized_distance
                    # 使用四次方函数 + 指数缩放大幅增强区分度
                    similarity = max(0.0, (base_similarity ** 4) * (2.0 ** base_similarity))

                    if similarity >= similarity_threshold:
                        # 更新分享次数
                        self._update_share_count(results['ids'][0][i])

                        memories.append({
                            'id': results['ids'][0][i],
                            'title': metadata.get('title', ''),
                            'content': doc,
                            'memory_type': metadata.get('memory_type', 'unknown'),
                            'category': metadata.get('category', ''),
                            'emotion': metadata.get('emotion', 'neutral'),
                            'importance': metadata.get('importance', 1.0),
                            'time_period': metadata.get('time_period', 'recent'),
                            'sharing_level': metadata.get('sharing_level', 1),
                            'similarity': similarity,
                            'share_count': metadata.get('share_count', 0) + 1,
                            'created_at': metadata.get('created_at')
                        })

            # 按相似度排序，相似度相同时按重要性排序
            memories.sort(key=lambda x: (x['similarity'], x['importance']), reverse=True)
            memories = memories[:limit]

            logger.info(f"🔍 个人记忆语义搜索 - 查询: {query_text[:30]}..., 原始结果: {len(results['documents'][0]) if results['documents'] and results['documents'][0] else 0} 条, 过滤后: {len(memories)} 条")
            if results['distances'] and results['distances'][0]:
                logger.info(f"🔍 距离范围: {min(results['distances'][0]):.3f} - {max(results['distances'][0]):.3f}")
                logger.info(f"🔍 相似度阈值: {similarity_threshold}")
            return memories

        except Exception as e:
            logger.error(f"💥 获取个人记忆失败 - 查询: {query_text}, 错误: {str(e)}")
            return []

    def _update_share_count(self, memory_id: str):
        """更新记忆分享次数"""
        try:
            # 获取当前记忆
            result = self.persona_collection.get(ids=[memory_id])
            if result['metadatas'] and result['metadatas'][0]:
                metadata = result['metadatas'][0]
                metadata['share_count'] = metadata.get('share_count', 0) + 1
                metadata['last_shared'] = datetime.now().isoformat()

                # 更新记忆
                self.persona_collection.update(
                    ids=[memory_id],
                    metadatas=[metadata]
                )
        except Exception as e:
            logger.warning(f"⚠️ 更新分享次数失败: {e}")

    def get_persona_memories_paginated(self, memory_type: str = None, max_sharing_level: int = 3,
                                     page: int = 1, page_size: int = 10) -> Dict:
        """
        分页获取个人记忆，按时间倒序排列

        Args:
            memory_type: 记忆类型过滤
            max_sharing_level: 最大分享等级
            page: 页码（从1开始）
            page_size: 每页数量

        Returns:
            包含记忆列表和分页信息的字典
        """
        try:
            # 构建过滤条件
            where_filter = {"sharing_level": {"$lte": max_sharing_level}}
            if memory_type:
                where_filter["memory_type"] = memory_type

            # 获取所有符合条件的记忆
            results = self.persona_collection.get(
                where=where_filter,
                include=['metadatas', 'documents']
            )

            if not results['ids'] or len(results['ids']) == 0:
                return {
                    'memories': [],
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': 0,
                        'total_pages': 0
                    }
                }

            # 将结果转换为记忆对象并按时间排序
            memories = []
            for i, memory_id in enumerate(results['ids']):
                metadata = results['metadatas'][i]
                content = results['documents'][i]

                memory = {
                    'id': memory_id,
                    'title': metadata.get('title', ''),
                    'content': content,
                    'memory_type': metadata.get('memory_type', 'unknown'),
                    'category': metadata.get('category', ''),
                    'emotion': metadata.get('emotion', 'neutral'),
                    'importance': metadata.get('importance', 1.0),
                    'time_period': metadata.get('time_period', 'recent'),
                    'sharing_level': metadata.get('sharing_level', 1),
                    'share_count': metadata.get('share_count', 0),
                    'created_at': metadata.get('created_at', ''),
                    'last_shared': metadata.get('last_shared', '')
                }
                memories.append(memory)

            # 按创建时间倒序排序
            memories.sort(key=lambda x: x['created_at'], reverse=True)

            # 计算分页信息
            total = len(memories)
            total_pages = (total + page_size - 1) // page_size
            start_index = (page - 1) * page_size
            end_index = start_index + page_size

            # 获取当前页的记忆
            page_memories = memories[start_index:end_index]

            logger.info(f"🔍 分页获取个人记忆 - 页码: {page}, 每页: {page_size}, 总数: {total}, 当前页: {len(page_memories)} 条")

            return {
                'memories': page_memories,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total': total,
                    'total_pages': total_pages
                }
            }

        except Exception as e:
            logger.error(f"💥 分页获取个人记忆失败: {e}")
            return {
                'memories': [],
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total': 0,
                    'total_pages': 0
                }
            }

    def get_persona_memory_statistics(self) -> Dict:
        """获取个人记忆统计信息"""
        try:
            # 获取所有个人记忆
            all_memories = self.persona_collection.get()

            if not all_memories['metadatas']:
                return {
                    'total_memories': 0,
                    'by_type': {},
                    'by_category': {},
                    'by_sharing_level': {},
                    'avg_importance': 0.0
                }

            # 统计信息
            total_memories = len(all_memories['metadatas'])
            type_stats = {}
            category_stats = {}
            sharing_stats = {}
            importance_sum = 0.0

            for metadata in all_memories['metadatas']:
                memory_type = metadata.get('memory_type', 'unknown')
                category = metadata.get('category', 'unknown')
                sharing_level = metadata.get('sharing_level', 1)
                importance = metadata.get('importance', 1.0)

                # 按类型统计
                type_stats[memory_type] = type_stats.get(memory_type, 0) + 1

                # 按分类统计
                category_stats[category] = category_stats.get(category, 0) + 1

                # 按分享等级统计
                sharing_stats[sharing_level] = sharing_stats.get(sharing_level, 0) + 1

                # 重要性统计
                importance_sum += importance

            return {
                'total_memories': total_memories,
                'by_type': type_stats,
                'by_category': category_stats,
                'by_sharing_level': sharing_stats,
                'avg_importance': importance_sum / total_memories if total_memories > 0 else 0.0
            }

        except Exception as e:
            logger.error(f"💥 获取个人记忆统计失败: {e}")
            return {
                'total_memories': 0,
                'by_type': {},
                'by_category': {},
                'by_sharing_level': {},
                'avg_importance': 0.0
            }

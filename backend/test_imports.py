#!/usr/bin/env python3
"""
测试重构后的导入是否正常
"""

import sys
import traceback

def test_import(module_name, description):
    """测试导入模块"""
    try:
        if module_name == "config":
            from config import Config
            print(f"✅ {description} - Config 导入成功")
        elif module_name == "embedding_client":
            from clients.embedding_client import EmbeddingClient
            print(f"✅ {description} - EmbeddingClient 导入成功")
        elif module_name == "vector_database":
            from models.vector_database import UserMemoryVectorDB, PersonaMemoryVectorDB
            print(f"✅ {description} - 向量数据库模型导入成功")
        elif module_name == "vector_memory_manager":
            from services.vector_memory_manager import VectorMemoryManager
            print(f"✅ {description} - VectorMemoryManager 导入成功")
        elif module_name == "persona_memory_manager":
            from services.persona_memory_manager import VectorPersonaMemoryManager
            print(f"✅ {description} - VectorPersonaMemoryManager 导入成功")
        else:
            print(f"❓ 未知模块: {module_name}")
            
    except Exception as e:
        print(f"❌ {description} - 导入失败: {e}")
        traceback.print_exc()
        return False
    return True

def main():
    """主测试函数"""
    print("🔍 开始测试重构后的模块导入...")
    print()
    
    tests = [
        ("config", "配置模块"),
        ("embedding_client", "嵌入客户端"),
        ("vector_database", "向量数据库模型"),
        ("vector_memory_manager", "向量记忆管理器"),
        ("persona_memory_manager", "个人记忆管理器"),
    ]
    
    success_count = 0
    for module_name, description in tests:
        if test_import(module_name, description):
            success_count += 1
        print()
    
    print(f"📊 测试结果: {success_count}/{len(tests)} 个模块导入成功")
    
    if success_count == len(tests):
        print("🎉 所有模块导入测试通过！")
        return True
    else:
        print("⚠️ 部分模块导入失败，需要检查")
        return False

if __name__ == "__main__":
    main()

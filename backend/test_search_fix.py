#!/usr/bin/env python3
"""
测试搜索修复
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

def test_search_fix():
    """测试搜索修复"""
    print("🔍 测试搜索修复...")

    try:
        # 直接测试API
        import requests

        # 测试分页获取记忆
        print("\n📄 测试分页获取记忆...")
        response = requests.get('http://localhost:8081/api/memories/persona?page=1&page_size=5')
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                memories = data['memories']
                pagination = data['pagination']
                print(f"✅ 获取到 {len(memories)} 条记忆")
                print(f"📊 分页信息: 第{pagination['page']}页，共{pagination['total']}条，{pagination['total_pages']}页")

                for i, memory in enumerate(memories[:3]):
                    print(f"  {i+1}. {memory['title']}")
                    print(f"     {memory['content'][:50]}...")
            else:
                print(f"❌ API返回错误: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")

        # 测试搜索功能
        print("\n🔍 测试搜索功能...")
        search_queries = ["画画", "艺术", "展览"]

        for query in search_queries:
            print(f"\n搜索: '{query}'")
            response = requests.get(f'http://localhost:8081/api/memories/persona?query={query}')
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    memories = data['memories']
                    print(f"✅ 搜索到 {len(memories)} 条记忆")

                    for i, memory in enumerate(memories[:3]):
                        similarity = memory.get('similarity', 0)
                        print(f"  {i+1}. {memory['title']} (相似度: {similarity:.3f})")
                        print(f"     {memory['content'][:50]}...")
                else:
                    print(f"❌ 搜索失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")

        print("\n✅ 搜索测试完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_search_fix()
